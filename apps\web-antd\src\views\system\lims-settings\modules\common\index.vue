<script setup lang="ts">
import { ref } from 'vue';

import { Anchor } from 'ant-design-vue';

// 当前激活的锚点
const activeAnchor = ref<string>('#smtp');

// 锚点项配置
const anchorItems = [
  {
    key: '#smtp',
    href: '#smtp',
    title: 'SMTP服务器设置',
  },
  {
    key: '#cache',
    href: '#cache',
    title: '客户端缓存设置',
  },
  {
    key: '#version',
    href: '#version',
    title: '版本控制设置',
  },
  {
    key: '#password',
    href: '#password',
    title: '全局密码策略',
  },
  {
    key: '#enterprise',
    href: '#enterprise',
    title: '企业设置',
  },
];

// 锚点变化事件
const handleAnchorChange = (currentActiveLink: string) => {
  activeAnchor.value = currentActiveLink;
};

// 锚点点击事件
const handleAnchorClick = (e: Event, link: { href: string; title: string }) => {
  e.preventDefault();
  // 滚动到指定位置
  const element = document.querySelector(link.href);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};
</script>

<template>
  <div class="anchor-demo">
    <div class="anchor-wrapper">
      <!-- Anchor 组件 -->
      <Anchor
        :current-link="activeAnchor"
        :offset-top="100"
        :bounds="5"
        replace
        @change="handleAnchorChange"
        @click="handleAnchorClick"
      >
        <Anchor.Link
          v-for="item in anchorItems"
          :key="item.key"
          :href="item.href"
          :title="item.title"
        />
      </Anchor>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <div id="smtp">
        <h2>SMTP服务器设置</h2>
      </div>
      <div id="cache">
        <h2>客户端缓存设置</h2>
      </div>
      <div id="version">
        <h2>版本控制设置</h2>
      </div>
      <div id="password">
        <h2>全局密码策略</h2>
      </div>
      <div id="enterprise">
        <h2>企业设置</h2>
      </div>
    </div>
  </div>
</template>

<style scoped>
.anchor-demo {
  display: flex;
  gap: 24px;
  height: 100vh;
  overflow: hidden;
}

.anchor-wrapper {
  flex-shrink: 0;
  width: 200px;
  padding-right: 16px;
  border-right: 1px solid #f0f0f0;
}

.content-area {
  flex: 1;
  padding: 0 24px;
  overflow-y: auto;
}

.section {
  min-height: 400px;
  margin-bottom: 32px;
}

.section h2 {
  padding-bottom: 8px;
  margin-bottom: 16px;
  color: #1890ff;
  border-bottom: 2px solid #f0f0f0;
}

.content-block {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.mock-content {
  margin-top: 16px;
}

.content-line {
  padding: 8px 0;
  border-bottom: 1px solid #e8e8e8;
}

.content-line:last-child {
  border-bottom: none;
}

/* 自定义 Anchor 样式 */
:deep(.ant-anchor) {
  padding-left: 0;
}

:deep(.ant-anchor-link-title) {
  font-size: 14px;
  color: #666;
}

:deep(.ant-anchor-link-active > .ant-anchor-link-title) {
  font-weight: 500;
  color: #1890ff;
}

:deep(.ant-anchor-ink-ball) {
  background-color: #1890ff;
}
</style>
