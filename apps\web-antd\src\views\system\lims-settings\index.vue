<script setup lang="ts">
import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { TabPane, Tabs } from 'ant-design-vue';

import AuditEvents from './modules/audit-events/index.vue';
import Common from './modules/common/index.vue';
import Counters from './modules/counters/index.vue';
import ExternalAuditTrail from './modules/external-audit-trail/index.vue';
import SystemStatus from './modules/system-status/index.vue';

const activeKey = ref('common');
</script>
<template>
  <Page auto-content-height>
    <Tabs
      v-model:active-key="activeKey"
      class="flex h-full flex-col [&_.ant-tabs-content-holder]:flex-1 [&_.ant-tabs-content]:h-full [&_.ant-tabs-tabpane]:h-full"
    >
      <TabPane key="common" tab="常规"><Common class="h-full" /></TabPane>
      <TabPane key="externalAudit" tab="外部审计跟踪配置">
        <ExternalAuditTrail class="h-full" />
      </TabPane>
      <TabPane key="auditEvent" tab="审计跟踪事件">
        <AuditEvents class="h-full" />
      </TabPane>
      <TabPane key="systemStatus" tab="系统状态">
        <SystemStatus class="h-full" />
      </TabPane>
      <TabPane key="counter" tab="计数器">
        <Counters class="h-full" />
      </TabPane>
    </Tabs>
  </Page>
</template>
<style scoped></style>
