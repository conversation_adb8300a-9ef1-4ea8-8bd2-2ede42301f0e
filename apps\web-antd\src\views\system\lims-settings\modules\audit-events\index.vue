<script lang="ts" setup>
import type { VxeGridDefines } from 'vxe-table/types/all';

import type { AuditEventApi } from './data';

import type { OnActionClickParams } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@vben-core/shadcn-ui';

import { Button, message, Modal, Space } from 'ant-design-vue';
import { Plus, RefreshCcw } from 'lucide-vue-next';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { $t } from '#/locales';
import { saveEditingRowOriginalData } from '#/utils/lims-grids-config';

import {
  useAuditEventColumns,
  useAuditEventDetailColumns,
  useAuditEventSearchSchema,
} from './data';
import AuditEventForm from './modules/audit-event-form.vue';

// 当前选中的审计事件
const selectedAuditEvent = ref<AuditEventApi.Item | null>(null);

// 审计事件Grid配置
const [AuditEventGrid, auditEventGridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useAuditEventSearchSchema(),
    submitOnChange: true,
  },
  gridEvents: {
    currentRowChange: async (
      params: VxeGridDefines.CurrentRowChangeParams<AuditEventApi.Item>,
    ) => {
      selectedAuditEvent.value = params.row;
      if (params.row) {
        // 联动加载审计事件详情
        await auditEventDetailGridApi.query();
      }
    },
    editActivated: saveEditingRowOriginalData,
    editClosed: async (params: any) => {
      // TODO: 触发保存审计事件数据
      console.log('保存审计事件编辑:', params);
    },
  },
  gridOptions: {
    columns: useAuditEventColumns(onAuditEventActionClick),
    height: 'auto',
    keepSource: true,
    rowConfig: {
      keyField: 'ORIGREC',
      isCurrent: true,
      isHover: true,
    },
    toolbarConfig: {
      slots: { buttons: 'auditEventButtons' },
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: false,
    },
    editConfig: {
      trigger: 'click',
      mode: 'cell',
      enabled: true,
    },
    menuConfig: {
      body: {
        options: [
          [
            {
              code: 'edit',
              name: '编辑',
              prefixConfig: { icon: 'vxe-icon-edit' },
            },
            {
              code: 'delete',
              name: '删除',
              prefixConfig: { icon: 'vxe-icon-delete' },
            },
          ],
        ],
      },
    },
    params: {
      limsControlId: 'dgAuditTrailEvents',
      tableName: 'AUDIT_TRAIL_EVENTS',
    },
    proxyConfig: {
      ajax: {
        query: async () => {
          // TODO: 实现审计事件数据查询API
          console.log('查询审计事件数据');
          return {
            items: [
              { MODULE: 'SAMPLE', EVENT_NAME: '样品登录', STATUS: 'Y' },
              { MODULE: 'TEST', EVENT_NAME: '检测完成', STATUS: 'Y' },
              { MODULE: 'RESULT', EVENT_NAME: '结果审核', STATUS: 'N' },
            ],
            total: 3,
          };
        },
      },
    },
  },
});

// 审计事件详情Grid配置
const [AuditEventDetailGrid, auditEventDetailGridApi] = useVbenVxeGrid({
  gridEvents: {
    editActivated: saveEditingRowOriginalData,
    editClosed: async (params: any) => {
      // TODO: 触发保存审计事件详情
      console.log('保存审计事件详情编辑:', params);
    },
  },
  gridOptions: {
    columns: useAuditEventDetailColumns(),
    height: 'auto',
    keepSource: true,
    rowConfig: {
      keyField: 'ORIGREC',
      isCurrent: true,
      isHover: true,
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: false,
      zoom: false,
    },
    editConfig: {
      trigger: 'click',
      mode: 'cell',
      enabled: true,
    },
    params: {
      limsControlId: 'dgAuditEventDetails',
      tableName: 'AUDIT_EVENT_DETAILS',
    },
    proxyConfig: {
      ajax: {
        query: async () => {
          if (!selectedAuditEvent.value) {
            return { items: [], total: 0 };
          }

          // TODO: 根据选中的审计事件查询详情
          console.log(
            '查询审计事件详情，事件:',
            selectedAuditEvent.value.EVENT_NAME,
          );
          return {
            items: [
              {
                TABLE_NAME: 'SAMPLE',
                FIELD_NAME: 'SAMPLE_ID',
                TRACK_TYPE: 'INSERT',
              },
              {
                TABLE_NAME: 'SAMPLE',
                FIELD_NAME: 'STATUS',
                TRACK_TYPE: 'UPDATE',
              },
            ],
            total: 2,
          };
        },
      },
    },
  },
});

// 表单组件引用
const [AuditEventFormDrawer, auditEventFormDrawerApi] = useVbenDrawer({
  connectedComponent: AuditEventForm,
  destroyOnClose: true,
});

// 审计事件操作处理
function onAuditEventActionClick(e: OnActionClickParams<AuditEventApi.Item>) {
  switch (e.code) {
    case 'delete': {
      onDeleteAuditEvent(e.row);
      break;
    }
    case 'edit': {
      onEditAuditEvent(e.row);
      break;
    }
  }
}

// 创建审计事件
function onCreateAuditEvent() {
  auditEventFormDrawerApi.setData({}).open();
}

// 编辑审计事件
function onEditAuditEvent(row: AuditEventApi.Item) {
  auditEventFormDrawerApi.setData(row).open();
}

// 删除审计事件
async function onDeleteAuditEvent(row: AuditEventApi.Item) {
  try {
    await Modal.confirm({
      title: '确认删除',
      content: `确定要删除审计事件"${row.EVENT_NAME}"吗？`,
    });

    message.loading({
      content: $t('ui.actionMessage.deleting', [row.EVENT_NAME]),
      duration: 0,
      key: 'delete_audit_event',
    });

    // TODO: 实现删除审计事件API
    console.log('删除审计事件:', row);

    message.success({
      content: $t('ui.actionMessage.deleteSuccess', [row.EVENT_NAME]),
      key: 'delete_audit_event',
    });

    onRefreshAuditEvents();
  } catch (error) {
    console.error('删除审计事件失败:', error);
    message.error({
      content: $t('ui.actionMessage.deleteFailed', [row.EVENT_NAME]),
      key: 'delete_audit_event',
    });
  }
}

// 刷新审计事件
function onRefreshAuditEvents() {
  auditEventGridApi.query();
}

// 刷新审计事件详情
function onRefreshAuditEventDetails() {
  auditEventDetailGridApi.query();
}

// 表单成功回调
function onFormSuccess() {
  onRefreshAuditEvents();
}
</script>

<template>
  <div class="h-full">
    <!-- 表单组件 -->
    <AuditEventFormDrawer @success="onFormSuccess" />

    <!-- 上下分割面板布局 -->
    <ResizablePanelGroup direction="vertical" class="h-full">
      <!-- 上方：审计事件列表 -->
      <ResizablePanel :default-size="60">
        <AuditEventGrid>
          <template #auditEventButtons>
            <Space>
              <Button type="primary" @click="onCreateAuditEvent">
                <Plus class="size-4" />
                {{ $t('system.limsSettings.auditEvents.action.add') }}
              </Button>
              <Button @click="onRefreshAuditEvents">
                <RefreshCcw class="size-4" />
                {{ $t('system.limsSettings.auditEvents.action.refresh') }}
              </Button>
            </Space>
          </template>
        </AuditEventGrid>
      </ResizablePanel>

      <ResizableHandle />

      <!-- 下方：审计事件详情 -->
      <ResizablePanel :default-size="40">
        <AuditEventDetailGrid />
      </ResizablePanel>
    </ResizablePanelGroup>
  </div>
</template>

<style scoped></style>
